# 文件下载功能扩展

## 概述

FileList 组件已扩展支持多种文件下载格式，用户可以通过下拉菜单选择不同的下载格式。

## 新增功能

### 支持的下载格式

1. **原始格式** - 下载文件的原始格式
2. **Markdown (.md)** - 将内容保存为 Markdown 文件
3. **HTML (.html)** - 转换为格式化的 HTML 文档，自动解析Markdown语法
4. **Word (.doc)** - 转换为Word兼容格式，自动解析Markdown语法，支持中文显示
5. **PDF (打印)** - 通过浏览器打印功能生成 PDF

### Markdown 自动解析功能

当下载HTML或Word格式时，系统会自动检测文件是否为Markdown格式（.md扩展名），如果是，则自动将Markdown语法转换为相应的HTML标签：

- **标题**：`# ## ###` → `<h1> <h2> <h3>`
- **粗体**：`**text**` → `<strong>text</strong>`
- **斜体**：`*text*` → `<em>text</em>`
- **代码**：`` `code` `` → `<code>code</code>`
- **代码块**：` ```code``` ` → `<pre><code>code</code></pre>`
- **链接**：`[text](url)` → `<a href="url">text</a>`
- **列表**：`- item` → `<ul><li>item</li></ul>`
- **引用**：`> text` → `<blockquote>text</blockquote>`
- **表格**：Markdown表格 → HTML表格
- **分隔线**：`---` → `<hr>`

### 用户界面改进

- 原来的单一下载按钮替换为带下拉箭头的按钮
- 每个下载选项都有对应的图标
- 鼠标悬停时显示提示信息
- 下拉菜单右对齐，避免超出容器边界

### 技术实现

#### 核心函数

```typescript
const downloadFileWithFormat = async (format: string) => {
  // 获取文件内容
  const response = await fetch(fileItem.url);
  const content = await response.text();
  
  // 根据格式进行转换和下载
  switch (format) {
    case 'markdown':
      downloadAsMarkdown(content, fileName);
      break;
    case 'html':
      downloadAsHtml(content, fileName);
      break;
    // ... 其他格式
  }
};
```

#### HTML 转换

HTML 格式包含完整的文档结构和样式：
- 自动检测并转换Markdown语法
- 响应式设计
- GitHub 风格的样式
- 代码高亮支持
- 表格和列表样式
- 支持中文字体

#### Word 格式

使用Word兼容的HTML格式：
- 自动检测并转换Markdown语法为Word格式
- 完全兼容 Microsoft Word 和 WPS
- 支持中文字符正确显示
- 包含UTF-8 BOM确保编码正确
- 保持文档格式和样式
- 标题自动应用Word样式
- 表格、列表、代码块正确格式化

#### PDF 生成

通过浏览器的打印功能：
- 使用Blob URL创建新窗口，避免安全警告
- 自动触发打印对话框
- 用户可选择保存为 PDF
- 支持中文字体和格式
- 自动清理临时资源

## 使用方法

1. 在文件列表中选择要下载的文件
2. 点击下载按钮（带下拉箭头）
3. 从下拉菜单中选择所需的下载格式
4. 文件将自动下载到本地

## 兼容性

- 支持所有现代浏览器
- 移动端友好
- 无需额外的依赖库

## 注意事项

- PDF 生成依赖浏览器的打印功能
- Word格式使用HTML实现，完全支持中文字符
- 大文件下载时可能需要等待时间
- 所有格式都正确处理UTF-8编码，避免乱码问题

## 修复的问题

### 中文乱码问题
- **问题**：下载Word格式时出现中文乱码
- **原因**：RTF格式不支持UTF-8编码
- **解决方案**：改用Word兼容的HTML格式，添加UTF-8 BOM，确保中文正确显示

### 安全警告问题
- **问题**：PDF打印功能使用document.write触发浏览器安全警告
- **解决方案**：改用Blob URL方式创建新窗口，避免安全警告
