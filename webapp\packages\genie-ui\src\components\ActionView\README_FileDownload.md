# 文件下载功能扩展

## 概述

FileList 组件已扩展支持多种文件下载格式，用户可以通过下拉菜单选择不同的下载格式。

## 新增功能

### 支持的下载格式

1. **原始格式** - 下载文件的原始格式
2. **Markdown (.md)** - 将内容保存为 Markdown 文件
3. **HTML (.html)** - 转换为格式化的 HTML 文档
4. **Word (.rtf)** - 转换为 RTF 格式，可被 Microsoft Word 打开
5. **PDF (打印)** - 通过浏览器打印功能生成 PDF

### 用户界面改进

- 原来的单一下载按钮替换为带下拉箭头的按钮
- 每个下载选项都有对应的图标
- 鼠标悬停时显示提示信息
- 下拉菜单右对齐，避免超出容器边界

### 技术实现

#### 核心函数

```typescript
const downloadFileWithFormat = async (format: string) => {
  // 获取文件内容
  const response = await fetch(fileItem.url);
  const content = await response.text();
  
  // 根据格式进行转换和下载
  switch (format) {
    case 'markdown':
      downloadAsMarkdown(content, fileName);
      break;
    case 'html':
      downloadAsHtml(content, fileName);
      break;
    // ... 其他格式
  }
};
```

#### HTML 转换

HTML 格式包含完整的文档结构和样式：
- 响应式设计
- GitHub 风格的 Markdown 样式
- 代码高亮支持
- 表格和列表样式

#### Word 格式

使用 RTF (Rich Text Format) 格式：
- 兼容 Microsoft Word
- 保持基本的文本格式
- 轻量级实现

#### PDF 生成

通过浏览器的打印功能：
- 打开新窗口显示格式化内容
- 自动触发打印对话框
- 用户可选择保存为 PDF

## 使用方法

1. 在文件列表中选择要下载的文件
2. 点击下载按钮（带下拉箭头）
3. 从下拉菜单中选择所需的下载格式
4. 文件将自动下载到本地

## 兼容性

- 支持所有现代浏览器
- 移动端友好
- 无需额外的依赖库

## 注意事项

- PDF 生成依赖浏览器的打印功能
- RTF 格式可能不支持复杂的 Markdown 语法
- 大文件下载时可能需要等待时间
