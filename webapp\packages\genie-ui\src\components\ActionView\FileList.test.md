# 测试文档下载功能

这是一个测试文档，用于验证新的下载功能是否正常工作。

## 功能特性

新的下载功能支持以下格式：

1. **原始格式** - 保持文件原有格式
2. **Markdown (.md)** - 下载为 Markdown 文件
3. **HTML (.html)** - 转换为 HTML 格式
4. **Word (.rtf)** - 转换为 RTF 格式（可被 Word 打开）
5. **PDF (打印)** - 通过浏览器打印功能生成 PDF

## 代码示例

```javascript
const downloadFile = (format) => {
  console.log(`下载格式: ${format}`);
};
```

## 表格示例

| 格式 | 扩展名 | 描述 |
|------|--------|------|
| Markdown | .md | 纯文本标记语言 |
| HTML | .html | 网页格式 |
| Word | .rtf | 富文本格式 |
| PDF | .pdf | 便携式文档格式 |

## 列表示例

- 支持多种下载格式
- 用户友好的下拉菜单
- 图标化的选项显示
- 响应式设计

> 这是一个引用块，用于测试样式。

**粗体文本** 和 *斜体文本* 的示例。

---

测试完成！
