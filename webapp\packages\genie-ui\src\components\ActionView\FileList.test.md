# 测试文档下载功能

这是一个测试文档，用于验证新的下载功能是否正常工作。

## 中文字符测试

测试各种中文字符和特殊符号：
- 简体中文：你好世界！这是一个测试文档。
- 繁体中文：您好世界！這是一個測試文檔。
- 特殊符号：《》【】""''——…
- 数字和英文：Hello World 123456
- 混合内容：这是English和中文的mixed content测试。

## 功能特性

新的下载功能支持以下格式：

1. **原始格式** - 保持文件原有格式
2. **Markdown (.md)** - 下载为 Markdown 文件
3. **HTML (.html)** - 转换为 HTML 格式，支持Markdown解析
4. **Word (.doc)** - 转换为Word格式，支持Markdown解析和中文显示
5. **PDF (打印)** - 通过浏览器打印功能生成 PDF

### Markdown格式测试

#### 标题测试
这是一个四级标题，用于测试标题转换功能。

##### 五级标题
###### 六级标题

#### 文本格式测试

这是普通段落文本。

**这是粗体文本**

*这是斜体文本*

`这是行内代码`

#### 列表测试

无序列表：
- 第一项
- 第二项
- 第三项

有序列表：
1. 第一项
2. 第二项
3. 第三项

#### 引用测试

> 这是一个引用块
> 可以包含多行内容

#### 链接测试

这是一个[链接示例](https://example.com)。

#### 代码块测试

```javascript
const downloadFile = (format) => {
  console.log(`下载格式: ${format}`);
  return true;
};
```

#### 分隔线测试

---

#### 表格测试

| 格式 | 扩展名 | 支持Markdown | 描述 |
|------|--------|-------------|------|
| Markdown | .md | ✅ | 纯文本标记语言 |
| HTML | .html | ✅ | 网页格式，支持Markdown转换 |
| Word | .doc | ✅ | Word格式，支持Markdown转换 |
| PDF | .pdf | ✅ | 便携式文档格式 |

## 代码示例

```javascript
const downloadFile = (format) => {
  console.log(`下载格式: ${format}`);
};
```

## 表格示例

| 格式 | 扩展名 | 描述 |
|------|--------|------|
| Markdown | .md | 纯文本标记语言 |
| HTML | .html | 网页格式 |
| Word | .rtf | 富文本格式 |
| PDF | .pdf | 便携式文档格式 |

## 列表示例

- 支持多种下载格式
- 用户友好的下拉菜单
- 图标化的选项显示
- 响应式设计

> 这是一个引用块，用于测试样式。

**粗体文本** 和 *斜体文本* 的示例。

---

测试完成！
