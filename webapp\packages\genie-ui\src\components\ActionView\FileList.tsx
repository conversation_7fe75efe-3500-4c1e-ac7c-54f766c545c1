import { copyText, downloadFile, formatTimestamp, showMessage } from "../../utils";
import { keyBy } from "lodash";
import React, { useMemo, useState } from "react";
import ActionViewFrame from "./ActionViewFrame";
import classNames from "classnames";
import { <PERSON><PERSON>enderer, HTMLRenderer, PanelItemType, TableRenderer } from "../ActionPanel";
import { Empty, Tooltip, Dropdown, Button } from "antd";
import { useBoolean, useMemoizedFn } from "ahooks";
import LoadingSpinner from "../LoadingSpinner";
import { DownloadOutlined, CaretDownOutlined, FileMarkdownOutlined, FileTextOutlined, FilePdfOutlined, FileWordOutlined } from "@ant-design/icons";
import type { MenuProps } from 'antd';

type FileItem = {
  name: string;
  messageTime?: string;
  type: string;
  task: PanelItemType;
  url: string;
};

const messageTypeEnum = ['file', 'code', 'html', 'markdown', 'result'];

const FileList: GenieType.FC<{
  taskList?: PanelItemType[];
  activeFile?: CHAT.TFile;
  clearActiveFile?: () => void;
}> = (props) => {
  const { taskList, clearActiveFile, activeFile } = props;

  const [ activeItem, setActiveItem ] = useState<string | undefined>();
  const [ copying, { setFalse: stopCopying, setTrue: startCopying } ] = useBoolean(false);

  const clearActive = useMemoizedFn(() => {
    clearActiveFile?.();
    setActiveItem(undefined);
  });

  const {list: fileList, map: fileMap } = useMemo(() => {
    let map: Record<string, FileItem> = {};
    const list = (taskList || []).reduce<FileItem[]>((pre, task) => {
      const { resultMap } = task;
      if (messageTypeEnum.includes(task.messageType)) {
        const fileInfo: FileItem[] = (resultMap?.fileInfo ?? resultMap.fileList ?? []).map((item) => {
          const extension = item.fileName?.split('.')?.pop();
          return {
            ...item,
            name: item.fileName!,
            url: item.domainUrl!,
            task,
            messageTime: formatTimestamp(task.messageTime),
            type: extension!
          };
        });
        pre.push(...fileInfo.filter((item) => !map[item.name]));

        map = keyBy(pre, 'fileName');
      }
      return pre;
    }, []);
    return {
      list,
      map
    };
  }, [taskList]);
  // 当前选中的文件
  const fileItem = activeFile || (activeItem ? fileMap[activeItem] : undefined);
  const generateQuery = (name?: string, noHover?: boolean, click?: () => void) => {
    return <div className="flex-1 flex items-center w-0 h-full">
      <span
        className={classNames("cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden", {'hover:font-medium': !noHover})}
        onClick={click || (() => setActiveItem(name))}
      >
        {name}
      </span>
    </div>;
  };

  let content: React.ReactNode = fileList.map((item) => (
    <div key={item.name} className="flex items-center pb-[16px]">
      <i className="font_family icon-rizhi mr-6"></i>
      {generateQuery(item.name)}
      <div className="text-[12px] text-[#8d8da5]">
        { item.messageTime}
      </div>
    </div>
  ));

  if (!fileList?.length) {
    content = <Empty />;
  }

  if (fileItem) {
    switch (fileItem.type) {
      case 'ppt':
      case 'html':
        content = <HTMLRenderer htmlUrl={fileItem.url} className="h-full" />;
        break;
      case 'csv':
      case 'xlsx':
        content = <TableRenderer fileUrl={fileItem.url} fileName={fileItem.name} />;
        break;
      default:
        content = <FileRenderer fileUrl={fileItem.url} fileName={fileItem.name} />;
        break;
    }
  }

  const copy = useMemoizedFn(async () => {
    if (!fileItem?.url) {
      return;
    }
    startCopying();
    const response = await fetch(fileItem.url);
    if (!response.ok) {
      stopCopying();
      throw new Error('Network response was not ok');
    }
    const data = await response.text();

    const copyData = data;

    // const parts = fileItem.name?.split('.');
    // const suffix = parts[parts.length - 1];
    // this.activeFileContent = data
    // const copyData = suffix === 'md' || suffix === 'txt' ? data : `\`\`\`${suffix}\n${data}\n\`\`\``;
    // this.markDownContent = this.md.render(
    //   suffix === 'md' || suffix === 'txt'
    //     ? data
    //     : `\`\`\`${suffix}\n${data}\n\`\`\``
    // )
    copyText(copyData);
    stopCopying();
    showMessage()?.success('复制成功');
  });

  // 下载文件内容并转换格式
  const downloadFileWithFormat = useMemoizedFn(async (format: string) => {
    if (!fileItem?.url || !fileItem?.name) {
      return;
    }

    try {
      const response = await fetch(fileItem.url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const content = await response.text();
      const baseName = fileItem.name.split('.')[0];

      switch (format) {
        case 'markdown':
          downloadAsMarkdown(content, `${baseName}.md`);
          break;
        case 'html':
          downloadAsHtml(content, `${baseName}.html`);
          break;
        case 'word':
          downloadAsWord(content, `${baseName}.docx`);
          break;
        case 'pdf':
          downloadAsPdf(content, `${baseName}.pdf`);
          break;
        case 'original':
        default:
          downloadFile(fileItem.url.replace('preview', 'download'), fileItem.name);
          break;
      }
      showMessage()?.success('下载成功');
    } catch (error) {
      console.error('Download failed:', error);
      showMessage()?.error('下载失败');
    }
  });

  // 下载为 Markdown 格式
  const downloadAsMarkdown = (content: string, fileName: string) => {
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载为 HTML 格式
  const downloadAsHtml = (content: string, fileName: string) => {
    // 创建完整的 HTML 文档
    const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${fileItem?.name || 'Document'}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 40px auto;
            max-width: 800px;
            line-height: 1.6;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 16px;
        }
        h1 { border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #eee; padding-bottom: 8px; }
        code {
            background-color: #f8f8f8;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f8f8f8;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e1e4e8;
        }
        pre code {
            background: none;
            padding: 0;
        }
        blockquote {
            border-left: 4px solid #dfe2e5;
            padding-left: 16px;
            margin-left: 0;
            color: #6a737d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        a {
            color: #0366d6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        ul, ol {
            padding-left: 24px;
        }
        li {
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <div class="content">
        <pre style="white-space: pre-wrap; word-wrap: break-word; background: none; border: none; padding: 0;">${content}</pre>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载为 Word 格式 (简单的 RTF 格式)
  const downloadAsWord = (content: string, fileName: string) => {
    // 创建简单的 RTF 格式内容
    const rtfContent = `{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}\\f0\\fs24 ${content.replace(/\n/g, '\\par ')}}`;
    const blob = new Blob([rtfContent], { type: 'application/rtf' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName.replace('.docx', '.rtf');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载为 PDF 格式 (使用浏览器打印功能)
  const downloadAsPdf = (content: string, fileName: string) => {
    // 创建一个新窗口用于打印
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${fileName}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <pre style="white-space: pre-wrap; word-wrap: break-word;">${content}</pre>
            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // 下载格式选项
  const downloadMenuItems: MenuProps['items'] = [
    {
      key: 'original',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DownloadOutlined />
          原始格式
        </span>
      ),
      onClick: () => downloadFileWithFormat('original'),
    },
    {
      key: 'markdown',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileMarkdownOutlined />
          Markdown (.md)
        </span>
      ),
      onClick: () => downloadFileWithFormat('markdown'),
    },
    {
      key: 'html',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileTextOutlined />
          HTML (.html)
        </span>
      ),
      onClick: () => downloadFileWithFormat('html'),
    },
    {
      key: 'word',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileWordOutlined />
          Word (.rtf)
        </span>
      ),
      onClick: () => downloadFileWithFormat('word'),
    },
    {
      key: 'pdf',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FilePdfOutlined />
          PDF (打印)
        </span>
      ),
      onClick: () => downloadFileWithFormat('pdf'),
    },
  ];

  return <ActionViewFrame
    className="p-16 overflow-y-auto"
    titleNode={fileItem && <>
      {generateQuery(fileItem?.name, true, clearActive)}
      <div className="flex items-center">

        <Dropdown
          menu={{ items: downloadMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Tooltip title="下载">
            <Button
              type="text"
              size="small"
              className="rounded-[4px] size-20 flex items-center justify-center mr-6 hover:bg-gray-200"
              icon={<DownloadOutlined />}
            />
          </Tooltip>
        </Dropdown>
        {/* excel文件不支持复制 */}
        {!['xlsx', 'xls'].includes(fileItem.type) && <Tooltip title="复制" placement="top">
          {copying ? <LoadingSpinner /> : <i className="font_family rounded-[4px] size-20 flex items-center justify-center icon-fuzhi cursor-pointer hover:bg-gray-200" onClick={copy}></i>}
        </Tooltip>}
      </div>
    </>}
    onClickTitle={() => setActiveItem(undefined)}
  >
    {content}
  </ActionViewFrame>;
};

export default FileList;

