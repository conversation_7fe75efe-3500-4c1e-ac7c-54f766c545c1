import { copyText, downloadFile, formatTimestamp, showMessage } from "../../utils";
import { keyBy } from "lodash";
import React, { useMemo, useState } from "react";
import ActionViewFrame from "./ActionViewFrame";
import classNames from "classnames";
import { <PERSON><PERSON>enderer, HTMLRenderer, PanelItemType, TableRenderer } from "../ActionPanel";
import { Empty, Tooltip, Dropdown, Button } from "antd";
import { useBoolean, useMemoizedFn } from "ahooks";
import LoadingSpinner from "../LoadingSpinner";
import { DownloadOutlined, CaretDownOutlined, FileMarkdownOutlined, FileTextOutlined, FilePdfOutlined, FileWordOutlined } from "@ant-design/icons";
import type { MenuProps } from 'antd';

type FileItem = {
  name: string;
  messageTime?: string;
  type: string;
  task: PanelItemType;
  url: string;
};

const messageTypeEnum = ['file', 'code', 'html', 'markdown', 'result'];

const FileList: GenieType.FC<{
  taskList?: PanelItemType[];
  activeFile?: CHAT.TFile;
  clearActiveFile?: () => void;
}> = (props) => {
  const { taskList, clearActiveFile, activeFile } = props;

  const [ activeItem, setActiveItem ] = useState<string | undefined>();
  const [ copying, { setFalse: stopCopying, setTrue: startCopying } ] = useBoolean(false);

  const clearActive = useMemoizedFn(() => {
    clearActiveFile?.();
    setActiveItem(undefined);
  });

  const {list: fileList, map: fileMap } = useMemo(() => {
    let map: Record<string, FileItem> = {};
    const list = (taskList || []).reduce<FileItem[]>((pre, task) => {
      const { resultMap } = task;
      if (messageTypeEnum.includes(task.messageType)) {
        const fileInfo: FileItem[] = (resultMap?.fileInfo ?? resultMap.fileList ?? []).map((item) => {
          const extension = item.fileName?.split('.')?.pop();
          return {
            ...item,
            name: item.fileName!,
            url: item.domainUrl!,
            task,
            messageTime: formatTimestamp(task.messageTime),
            type: extension!
          };
        });
        pre.push(...fileInfo.filter((item) => !map[item.name]));

        map = keyBy(pre, 'fileName');
      }
      return pre;
    }, []);
    return {
      list,
      map
    };
  }, [taskList]);
  // 当前选中的文件
  const fileItem = activeFile || (activeItem ? fileMap[activeItem] : undefined);
  const generateQuery = (name?: string, noHover?: boolean, click?: () => void) => {
    return <div className="flex-1 flex items-center w-0 h-full">
      <span
        className={classNames("cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden", {'hover:font-medium': !noHover})}
        onClick={click || (() => setActiveItem(name))}
      >
        {name}
      </span>
    </div>;
  };

  let content: React.ReactNode = fileList.map((item) => (
    <div key={item.name} className="flex items-center pb-[16px]">
      <i className="font_family icon-rizhi mr-6"></i>
      {generateQuery(item.name)}
      <div className="text-[12px] text-[#8d8da5]">
        { item.messageTime}
      </div>
    </div>
  ));

  if (!fileList?.length) {
    content = <Empty />;
  }

  if (fileItem) {
    switch (fileItem.type) {
      case 'ppt':
      case 'html':
        content = <HTMLRenderer htmlUrl={fileItem.url} className="h-full" />;
        break;
      case 'csv':
      case 'xlsx':
        content = <TableRenderer fileUrl={fileItem.url} fileName={fileItem.name} />;
        break;
      default:
        content = <FileRenderer fileUrl={fileItem.url} fileName={fileItem.name} />;
        break;
    }
  }

  const copy = useMemoizedFn(async () => {
    if (!fileItem?.url) {
      return;
    }
    startCopying();
    const response = await fetch(fileItem.url);
    if (!response.ok) {
      stopCopying();
      throw new Error('Network response was not ok');
    }
    const data = await response.text();

    const copyData = data;

    // const parts = fileItem.name?.split('.');
    // const suffix = parts[parts.length - 1];
    // this.activeFileContent = data
    // const copyData = suffix === 'md' || suffix === 'txt' ? data : `\`\`\`${suffix}\n${data}\n\`\`\``;
    // this.markDownContent = this.md.render(
    //   suffix === 'md' || suffix === 'txt'
    //     ? data
    //     : `\`\`\`${suffix}\n${data}\n\`\`\``
    // )
    copyText(copyData);
    stopCopying();
    showMessage()?.success('复制成功');
  });

  // 下载文件内容并转换格式
  const downloadFileWithFormat = useMemoizedFn(async (format: string) => {
    if (!fileItem?.url || !fileItem?.name) {
      return;
    }

    try {
      const response = await fetch(fileItem.url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const content = await response.text();
      const baseName = fileItem.name.split('.')[0];

      switch (format) {
        case 'markdown':
          downloadAsMarkdown(content, `${baseName}.md`);
          break;
        case 'html':
          downloadAsHtml(content, `${baseName}.html`);
          break;
        case 'word':
          downloadAsWord(content, `${baseName}.docx`);
          break;
        case 'pdf':
          downloadAsPdf(content, `${baseName}.pdf`);
          break;
        case 'original':
        default:
          downloadFile(fileItem.url.replace('preview', 'download'), fileItem.name);
          break;
      }
      showMessage()?.success('下载成功');
    } catch (error) {
      console.error('Download failed:', error);
      showMessage()?.error('下载失败');
    }
  });

  // 下载为 Markdown 格式
  const downloadAsMarkdown = (content: string, fileName: string) => {
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载为 HTML 格式
  const downloadAsHtml = (content: string, fileName: string) => {
    // 检查是否是Markdown文件，如果是则转换为HTML
    const isMarkdown = fileItem?.type === 'md' || fileName.endsWith('.md');
    const processedContent = isMarkdown ? convertMarkdownToHtml(content) : `<pre style="white-space: pre-wrap; word-wrap: break-word;">${content}</pre>`;

    // 创建完整的 HTML 文档
    const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${fileItem?.name || 'Document'}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Microsoft YaHei', sans-serif;
            margin: 40px auto;
            max-width: 800px;
            line-height: 1.6;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 16px;
        }
        h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            font-size: 2em;
        }
        h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            font-size: 1.5em;
        }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1em; }
        h5 { font-size: 0.875em; }
        h6 { font-size: 0.85em; }
        p {
            margin: 16px 0;
        }
        code {
            background-color: #f6f8fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e1e4e8;
            margin: 16px 0;
        }
        pre code {
            background: none;
            padding: 0;
            border-radius: 0;
        }
        blockquote {
            border-left: 4px solid #dfe2e5;
            padding-left: 16px;
            margin: 16px 0;
            color: #6a737d;
            font-style: italic;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        a {
            color: #0366d6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        ul, ol {
            padding-left: 24px;
            margin: 16px 0;
        }
        li {
            margin: 4px 0;
        }
        hr {
            border: none;
            border-top: 1px solid #e1e4e8;
            margin: 24px 0;
        }
        strong {
            font-weight: 600;
        }
        em {
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="content">
        ${processedContent}
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 简单的Markdown到HTML转换函数
  const convertMarkdownToHtml = (markdown: string): string => {
    let html = markdown;

    // 转换标题 (# ## ### 等)
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

    // 转换粗体 **text**
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // 转换斜体 *text*
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 转换代码块 ```code```
    html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // 转换行内代码 `code`
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // 转换链接 [text](url)
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // 转换无序列表
    html = html.replace(/^[\s]*[-*+] (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

    // 转换有序列表
    html = html.replace(/^[\s]*\d+\. (.+)$/gm, '<li>$1</li>');

    // 转换引用块
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');

    // 转换水平线
    html = html.replace(/^---$/gm, '<hr>');

    // 转换表格
    const tableRegex = /^\|(.+)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm;
    html = html.replace(tableRegex, (_match, header, rows) => {
      const headerCells = header.split('|').map((cell: string) => `<th>${cell.trim()}</th>`).join('');
      const rowsHtml = rows.trim().split('\n').map((row: string) => {
        const cells = row.split('|').slice(1, -1).map((cell: string) => `<td>${cell.trim()}</td>`).join('');
        return `<tr>${cells}</tr>`;
      }).join('');
      return `<table><thead><tr>${headerCells}</tr></thead><tbody>${rowsHtml}</tbody></table>`;
    });

    // 转换段落 (将连续的非HTML行包装在<p>标签中)
    const lines = html.split('\n');
    const processedLines: string[] = [];
    let inParagraph = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 检查是否是HTML标签行
      const isHtmlLine = /^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/.test(line) ||
                        /<\/(h[1-6]|ul|ol|li|blockquote|pre|table|div|p)>$/.test(line);

      if (line === '') {
        if (inParagraph) {
          processedLines.push('</p>');
          inParagraph = false;
        }
        processedLines.push('');
      } else if (isHtmlLine) {
        if (inParagraph) {
          processedLines.push('</p>');
          inParagraph = false;
        }
        processedLines.push(line);
      } else {
        if (!inParagraph) {
          processedLines.push('<p>');
          inParagraph = true;
        }
        processedLines.push(line);
      }
    }

    if (inParagraph) {
      processedLines.push('</p>');
    }

    return processedLines.join('\n');
  };

  // 下载为 Word 格式 (使用HTML格式，可被Word打开)
  const downloadAsWord = (content: string, fileName: string) => {
    // 检查是否是Markdown文件，如果是则转换为HTML
    const isMarkdown = fileItem?.type === 'md' || fileName.endsWith('.md');
    const htmlContent = isMarkdown ? convertMarkdownToHtml(content) : content;

    // 创建Word兼容的HTML格式
    const wordHtmlContent = `
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office"
      xmlns:w="urn:schemas-microsoft-com:office:word"
      xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <title>${fileItem?.name || 'Document'}</title>
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        @page {
            size: A4;
            margin: 2.54cm;
        }
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.5;
            color: #000000;
            background: white;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 18pt;
            margin-bottom: 12pt;
            font-weight: bold;
            page-break-after: avoid;
        }
        h1 {
            font-size: 20pt;
            border-bottom: 2pt solid #2c3e50;
            padding-bottom: 6pt;
        }
        h2 {
            font-size: 16pt;
            border-bottom: 1pt solid #cccccc;
            padding-bottom: 3pt;
        }
        h3 { font-size: 14pt; }
        h4 { font-size: 13pt; }
        h5 { font-size: 12pt; }
        h6 { font-size: 11pt; }
        p {
            margin: 6pt 0;
            text-align: justify;
            text-indent: 0;
        }
        ul, ol {
            margin: 6pt 0;
            padding-left: 24pt;
        }
        li {
            margin: 3pt 0;
        }
        pre {
            font-family: "Courier New", "Consolas", monospace;
            background-color: #f8f8f8;
            border: 1pt solid #e1e4e8;
            padding: 12pt;
            margin: 12pt 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        code {
            font-family: "Courier New", "Consolas", monospace;
            background-color: #f8f8f8;
            padding: 2pt 4pt;
            border-radius: 3pt;
            font-size: 11pt;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 12pt 0;
        }
        th, td {
            border: 1pt solid #000000;
            padding: 6pt;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f6f8fa;
            font-weight: bold;
        }
        blockquote {
            border-left: 4pt solid #dfe2e5;
            padding-left: 12pt;
            margin: 12pt 0;
            color: #6a737d;
            font-style: italic;
        }
        hr {
            border: none;
            border-top: 1pt solid #e1e4e8;
            margin: 18pt 0;
        }
        a {
            color: #0366d6;
            text-decoration: underline;
        }
        strong {
            font-weight: bold;
        }
        em {
            font-style: italic;
        }
    </style>
</head>
<body>
    <div>
        ${htmlContent}
    </div>
</body>
</html>`;

    // 使用UTF-8 BOM确保中文正确显示
    const bom = '\ufeff';
    const blob = new Blob([bom + wordHtmlContent], {
      type: 'application/msword;charset=utf-8'
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName.replace('.rtf', '.doc');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载为 PDF 格式 (使用浏览器打印功能)
  const downloadAsPdf = (content: string, fileName: string) => {
    // 创建打印用的HTML内容
    const printHtml = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${fileName}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
            font-size: 12pt;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 12px;
            page-break-after: avoid;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: "Microsoft YaHei", "SimSun", "Courier New", monospace;
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e1e4e8;
            page-break-inside: avoid;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
        <p>请使用浏览器的打印功能（Ctrl+P）并选择"保存为PDF"</p>
        <button onclick="window.print()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">打印/保存为PDF</button>
        <button onclick="window.close()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">关闭</button>
    </div>
    <pre>${content}</pre>
    <script>
        // 自动打印功能
        window.addEventListener('load', function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        });

        // 打印后关闭窗口
        window.addEventListener('afterprint', function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        });
    </script>
</body>
</html>`;

    // 使用Blob URL创建新窗口
    const blob = new Blob([printHtml], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const printWindow = window.open(url, '_blank', 'width=800,height=600');

    // 清理URL
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 5000);
  };

  // 下载格式选项
  const downloadMenuItems: MenuProps['items'] = [
    {
      key: 'original',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DownloadOutlined />
          原始格式
        </span>
      ),
      onClick: () => downloadFileWithFormat('original'),
    },
    {
      key: 'markdown',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileMarkdownOutlined />
          Markdown (.md)
        </span>
      ),
      onClick: () => downloadFileWithFormat('markdown'),
    },
    {
      key: 'html',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileTextOutlined />
          HTML (.html)
        </span>
      ),
      onClick: () => downloadFileWithFormat('html'),
    },
    {
      key: 'word',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileWordOutlined />
          Word (.doc)
        </span>
      ),
      onClick: () => downloadFileWithFormat('word'),
    },
    {
      key: 'pdf',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FilePdfOutlined />
          PDF (打印)
        </span>
      ),
      onClick: () => downloadFileWithFormat('pdf'),
    },
  ];

  return <ActionViewFrame
    className="p-16 overflow-y-auto"
    titleNode={fileItem && <>
      {generateQuery(fileItem?.name, true, clearActive)}
      <div className="flex items-center">

        <Dropdown
          menu={{ items: downloadMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Tooltip title="选择下载格式">
            <Button
              type="text"
              size="small"
              className="rounded-[4px] h-20 px-2 flex items-center justify-center mr-6 hover:bg-gray-200"
              style={{ minWidth: 'auto' }}
            >
              <DownloadOutlined />
              <CaretDownOutlined style={{ fontSize: '10px', marginLeft: '2px' }} />
            </Button>
          </Tooltip>
        </Dropdown>
        {/* excel文件不支持复制 */}
        {!['xlsx', 'xls'].includes(fileItem.type) && <Tooltip title="复制" placement="top">
          {copying ? <LoadingSpinner /> : <i className="font_family rounded-[4px] size-20 flex items-center justify-center icon-fuzhi cursor-pointer hover:bg-gray-200" onClick={copy}></i>}
        </Tooltip>}
      </div>
    </>}
    onClickTitle={() => setActiveItem(undefined)}
  >
    {content}
  </ActionViewFrame>;
};

export default FileList;

