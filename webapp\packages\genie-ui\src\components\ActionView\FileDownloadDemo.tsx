import React from 'react';
import { Card, Typography, Space } from 'antd';
import FileList from './FileList';

const { Title, Paragraph } = Typography;

// 模拟文件数据
const mockTaskList = [
  {
    messageType: 'file',
    messageTime: Date.now(),
    resultMap: {
      fileInfo: [
        {
          fileName: 'sample-document.md',
          domainUrl: 'https://example.com/files/sample-document.md',
        },
        {
          fileName: 'report.html',
          domainUrl: 'https://example.com/files/report.html',
        },
        {
          fileName: 'data-analysis.txt',
          domainUrl: 'https://example.com/files/data-analysis.txt',
        }
      ]
    }
  }
];

const FileDownloadDemo: React.FC = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>文件下载功能演示</Title>
        <Paragraph>
          这个演示展示了扩展后的文件下载功能。点击文件列表中的下载按钮，
          可以选择不同的下载格式：
        </Paragraph>
        
        <Space direction="vertical" size="small" style={{ marginBottom: '24px' }}>
          <li><strong>原始格式</strong> - 下载文件的原始格式</li>
          <li><strong>Markdown (.md)</strong> - 保存为 Markdown 文件</li>
          <li><strong>HTML (.html)</strong> - 转换为格式化的 HTML 文档</li>
          <li><strong>Word (.rtf)</strong> - 转换为 RTF 格式</li>
          <li><strong>PDF (打印)</strong> - 通过浏览器打印功能生成 PDF</li>
        </Space>

        <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', overflow: 'hidden' }}>
          <FileList 
            taskList={mockTaskList}
            activeFile={undefined}
            clearActiveFile={() => {}}
          />
        </div>
      </Card>
    </div>
  );
};

export default FileDownloadDemo;
